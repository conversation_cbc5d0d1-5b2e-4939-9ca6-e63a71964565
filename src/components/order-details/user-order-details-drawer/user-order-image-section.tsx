import { GlassWrapper } from '@/components/shared/glass-wrapper';
import { TgsOrImage } from '@/components/tgs/tgs-or-image';
import { TgsOrImageGift } from '@/components/tgs/tgs-or-image-gift';
import { TgsSkeleton } from '@/components/tgs/tgs-skeleton';
import { useOrderGift } from '@/hooks/use-order-gift';
import { cn } from '@/lib/utils';
import type {
  CollectionEntity,
  OrderEntity,
} from '@/mikerudenko/marketplace-shared';

interface UserOrderImageSectionProps {
  collection: CollectionEntity | null;
  order: OrderEntity;
  top?: React.ReactNode;
  bottom?: React.ReactNode;
}

export function UserOrderImageSection({
  collection,
  order,
  bottom,
  top,
}: UserOrderImageSectionProps) {
  const { gift, loading } = useOrderGift(order);

  // Only show skeleton if we're actively loading AND expect a gift but don't have it yet
  // If we have gift data OR we know there's no gift, show content immediately
  const shouldShowSkeleton = loading && order.gift_id_list && !gift;

  return (
    <div className="relative">
      {/* Rainbow Smoke Animation */}
      <div className="absolute inset-0 z-0 rounded-[40px] overflow-hidden">
        <div className="smoke-container absolute inset-0">
          {[...Array(8)].map((_, i) => (
            <div
              key={i}
              className="smoke-particle absolute"
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${i * 0.5}s`,
                animationDuration: `${3 + Math.random() * 2}s`,
              }}
            />
          ))}
        </div>
      </div>

      <GlassWrapper
        variant="default"
        intensity="light"
        className="bg-[#17212b]/30 border-b border-[#3a4a5c]/50 rounded-[40px] py-3 relative z-10"
        enableHover={false}
      >
        <div
          className={cn(
            'aspect-square relative rounded-2xl overflow-hidden bg-gradient-to-br border border-[#3a4a5c]/50 p-8',
            gift && 'p-0',
          )}
        >
          {top && (
            <div className="w-full flex absolute top-0 left-0 px-2 pt-2">
              {top}
            </div>
          )}
          {shouldShowSkeleton ? (
            <TgsSkeleton
              className="w-full h-full"
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
              }}
            />
          ) : gift ? (
            <TgsOrImageGift
              isImage={false}
              gift={gift}
              className="w-full h-full"
              style={{ width: '100%', height: '100%' }}
            />
          ) : collection ? (
            <TgsOrImage
              isImage={false}
              collectionId={collection.id}
              imageProps={{
                alt: collection.name || 'Order item',
                fill: true,
                className: 'object-contain drop-shadow-2xl',
              }}
              tgsProps={{
                style: { height: '100%', width: '100%' },
              }}
            />
          ) : (
            <div className="w-full h-full bg-[#3a4a5c] rounded flex items-center justify-center">
              <div className="w-16 h-16 bg-[#17212b] rounded" />
            </div>
          )}
          {bottom && (
            <div className="w-full flex absolute bottom-0 left-0">{bottom}</div>
          )}
        </div>
      </GlassWrapper>

      <style jsx>{`
        .smoke-container {
          filter: blur(1px);
        }

        .smoke-particle {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          background: linear-gradient(
            45deg,
            #ff0080,
            #ff8000,
            #ffff00,
            #80ff00,
            #00ff80,
            #0080ff,
            #8000ff,
            #ff0080
          );
          background-size: 200% 200%;
          animation:
            smokeRise linear infinite,
            colorShift 2s ease-in-out infinite;
          opacity: 0.6;
          transform-origin: center;
        }

        .smoke-particle:nth-child(odd) {
          animation-direction: normal, reverse;
        }

        .smoke-particle:nth-child(even) {
          width: 40px;
          height: 40px;
          opacity: 0.4;
        }

        @keyframes smokeRise {
          0% {
            transform: translateY(100vh) scale(0.5) rotate(0deg);
            opacity: 0;
          }
          10% {
            opacity: 0.6;
          }
          90% {
            opacity: 0.6;
          }
          100% {
            transform: translateY(-20vh) scale(1.5) rotate(360deg);
            opacity: 0;
          }
        }

        @keyframes colorShift {
          0%,
          100% {
            background-position: 0% 50%;
            filter: hue-rotate(0deg);
          }
          25% {
            background-position: 100% 50%;
            filter: hue-rotate(90deg);
          }
          50% {
            background-position: 100% 100%;
            filter: hue-rotate(180deg);
          }
          75% {
            background-position: 0% 100%;
            filter: hue-rotate(270deg);
          }
        }
      `}</style>
    </div>
  );
}
