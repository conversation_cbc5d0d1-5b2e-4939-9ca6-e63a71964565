'use client';

import { Button as TgButton } from '@telegram-apps/telegram-ui';
import { ChevronDown, LogOut } from 'lucide-react';
import { useIntl } from 'react-intl';

import { GlassWrapper } from '@/components/shared/glass-wrapper';
import { TonLogo } from '@/components/TonLogo';
import { cn } from '@/lib/utils';

import { tonConnectButtonMessages } from './intl/ton-connect-button.messages';

interface TonConnectButtonProps {
  tonWalletAddress: string;
  isConnecting: boolean;
  isAuthenticating: boolean;
  showWalletDropdown?: boolean;
  setShowWalletDropdown?: (show: boolean) => void;
  dropdownRef?: React.RefObject<HTMLDivElement | null>;
  onWalletAction: () => Promise<string | void>;
  onDisconnectWallet: () => Promise<void>;
  formatAddress: (address: string) => string;
  className?: string;
  size?: 'default' | 'compact';
  showDropdown?: boolean;
}

export function TonConnectButton({
  tonWalletAddress,
  isConnecting,
  isAuthenticating,
  showWalletDropdown = false,
  setShowWalletDropdown,
  dropdownRef,
  onWalletAction,
  onDisconnectWallet,
  formatAddress,
  className = '',
  size = 'default',
  showDropdown = true,
}: TonConnectButtonProps) {
  const { formatMessage: t } = useIntl();

  const handleWalletClick = async () => {
    const result = await onWalletAction();
    if (result === 'show-dropdown' && setShowWalletDropdown) {
      setShowWalletDropdown(!showWalletDropdown);
    }
  };

  const handleDisconnect = async () => {
    await onDisconnectWallet();
    if (setShowWalletDropdown) {
      setShowWalletDropdown(false);
    }
  };

  const isCompact = size === 'compact';
  const buttonHeight = isCompact ? 'h-8' : 'h-10';
  const logoSize = isCompact ? 20 : 24;
  const textSize = isCompact ? 'text-sm' : 'text-base';

  return (
    <div className={cn('relative', className)} ref={dropdownRef}>
      <TgButton
        className={cn(
          '[&>h6]:flex [&>h6]:items-center [&>h6]:gap-1 bg-[#27A3E9]!',
          'rounded-full!',
          buttonHeight,
          'max-w-[110px] xs:max-w-fit',
          'px-3!',
        )}
        onClick={handleWalletClick}
        disabled={isConnecting || isAuthenticating}
      >
        <TonLogo size={logoSize} />
        <span className={cn('truncate -ml-1', textSize)}>
          {isAuthenticating
            ? t(tonConnectButtonMessages.authenticating)
            : isConnecting
              ? t(tonConnectButtonMessages.connecting)
              : tonWalletAddress
                ? formatAddress(tonWalletAddress)
                : t(tonConnectButtonMessages.connect)}
        </span>
        {tonWalletAddress && showDropdown && (
          <ChevronDown className="w-3 h-3 ml-1" />
        )}
      </TgButton>

      {showWalletDropdown && tonWalletAddress && showDropdown && (
        <div className="absolute right-0 top-full mt-1 min-w-[120px] z-50">
          <GlassWrapper
            variant="default"
            intensity="medium"
            className="bg-[#232e3c]/30 border border-[#3a4a5c]/50 py-1"
            enableHover={false}
          >
            <button
              onClick={handleDisconnect}
              className="w-full px-3 py-2 text-left text-sm text-[#f5f5f5] hover:bg-[#6ab2f2]/20 hover:text-white flex items-center gap-2 transition-colors duration-200"
            >
              <LogOut className="w-3 h-3" />
              {t(tonConnectButtonMessages.disconnect)}
            </button>
          </GlassWrapper>
        </div>
      )}
    </div>
  );
}
