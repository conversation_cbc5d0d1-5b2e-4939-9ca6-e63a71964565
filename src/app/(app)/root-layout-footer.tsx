'use client';

import { Gift, ShoppingCart, Store, User } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useIntl } from 'react-intl';

import { GlassWrapper } from '@/components/shared/glass-wrapper';
import { AppRoutes } from '@/core.constants';
import { cn } from '@/lib/utils';

import { rootLayoutFooterMessages } from './intl/root-layout-footer.messages';

export default function RootLayoutFooter() {
  const { formatMessage: t } = useIntl();
  const pathname = usePathname();

  const allNavItems = [
    {
      icon: Store,
      label: t(rootLayoutFooterMessages.marketplace),
      route: AppRoutes.MARKETPLACE,
      active: pathname === AppRoutes.MARKETPLACE,
    },
    {
      icon: ShoppingCart,
      label: t(rootLayoutFooterMessages.myOrders),
      route: AppRoutes.ORDERS,
      active: pathname === AppRoutes.ORDERS,
    },
    {
      icon: Gift,
      label: t(rootLayoutFooterMessages.myGifts),
      route: AppRoutes.GIFTS,
      active: pathname === AppRoutes.GIFTS,
    },
    {
      icon: User,
      label: t(rootLayoutFooterMessages.myProfile),
      route: AppRoutes.PROFILE,
      active: pathname === AppRoutes.PROFILE,
    },
  ];

  const navItems = allNavItems;

  return (
    <footer className="fixed bottom-0 left-0 right-0 z-50 p-1">
      <GlassWrapper variant="footer" className="min-h-[50px]">
        <div className="flex items-center justify-between h-full w-full">
          {navItems.map((item) => {
            const IconComponent = item.icon;
            return (
              <Link
                key={item.route}
                href={item.route}
                className={cn(
                  'flex flex-col flex-1 items-center gap-1 p-2 h-auto transition-all duration-500 ease-out transform hover:scale-105',
                  item.active
                    ? 'text-white border-2 border-white rounded-2xl scale-102 drop-shadow-lg'
                    : 'text-white hover:text-blue-200 hover:scale-105',
                )}
              >
                <div className={cn('transition-all duration-300')}>
                  <IconComponent className="w-7 h-7" />
                </div>
              </Link>
            );
          })}
        </div>
      </GlassWrapper>
    </footer>
  );
}
